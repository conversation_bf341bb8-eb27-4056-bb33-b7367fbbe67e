package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.dto.PushTokenInfo;
import co.com.gedsys.authentication.dto.PushTokenRequest;
import co.com.gedsys.authentication.entity.PushToken;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.service.AuthenticationService;
import co.com.gedsys.authentication.service.PushTokenService;
import co.com.gedsys.authentication.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

/**
 * REST controller for push token management.
 * Handles push token registration, retrieval, and deletion for mobile sessions only.
 */
@RestController
@RequestMapping("/auth/push-token")
public class PushTokenController {
    
    private static final Logger logger = LoggerFactory.getLogger(PushTokenController.class);
    
    private final PushTokenService pushTokenService;
    private final UserService userService;
    private final AuthenticationService authenticationService;
    
    public PushTokenController(PushTokenService pushTokenService, 
                              UserService userService,
                              AuthenticationService authenticationService) {
        this.pushTokenService = pushTokenService;
        this.userService = userService;
        this.authenticationService = authenticationService;
    }
    
    /**
     * Register or update push token for the authenticated user.
     * Only available for users with active mobile sessions.
     * 
     * @param pushTokenRequest the push token registration request
     * @return success response with push token info
     */
    @PostMapping
    public ResponseEntity<?> registerPushToken(@Valid @RequestBody PushTokenRequest pushTokenRequest) {
        logger.debug("SECURITY_EVENT: Push token registration request - received push token registration request");
        
        try {
            User currentUser = getCurrentUser();
            
            // Validate that user has an active mobile session
            if (!authenticationService.hasActiveSession(currentUser, SessionType.MOBILE)) {
                logger.warn("SECURITY_EVENT: Push token registration denied - user: {} attempted registration without active mobile session", 
                           currentUser.getId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Push tokens can only be managed from mobile sessions"));
            }
            
            // Validate device type for mobile
            pushTokenService.validateMobileDeviceType(pushTokenRequest.getDeviceType());
            
            // Register or update push token
            PushToken pushToken = pushTokenService.registerPushToken(
                    currentUser,
                    pushTokenRequest.getPushToken(),
                    pushTokenRequest.getDeviceType(),
                    pushTokenRequest.getDeviceId()
            );
            
            // Create response
            PushTokenInfo pushTokenInfo = new PushTokenInfo(pushToken);
            
            logger.info("SECURITY_EVENT: Push token registration successful - user: {} registered push token for device type: {}", 
                       currentUser.getId(), pushTokenRequest.getDeviceType());
            
            return ResponseEntity.ok(Map.of(
                    "message", "Push token registered successfully",
                    "pushToken", pushTokenInfo
            ));
            
        } catch (IllegalArgumentException e) {
            logger.warn("SECURITY_EVENT: Push token registration failed - invalid request: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Push token registration error - unexpected error: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to register push token"));
        }
    }
    
    /**
     * Get push token information for the authenticated user.
     * Only available for users with active mobile sessions.
     * 
     * @return push token information if exists
     */
    @GetMapping
    public ResponseEntity<?> getPushToken() {
        logger.debug("Push token retrieval request received");
        
        try {
            User currentUser = getCurrentUser();
            
            // Validate that user has an active mobile session
            if (!authenticationService.hasActiveSession(currentUser, SessionType.MOBILE)) {
                logger.warn("Push token retrieval attempted by user {} without active mobile session", 
                           currentUser.getId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Push tokens can only be accessed from mobile sessions"));
            }
            
            // Get push token
            Optional<PushToken> pushTokenOpt = pushTokenService.getPushTokenByUser(currentUser);
            
            if (pushTokenOpt.isEmpty()) {
                logger.debug("No push token found for user {}", currentUser.getId());
                return ResponseEntity.ok(Map.of(
                        "message", "No push token registered",
                        "pushToken", null
                ));
            }
            
            PushTokenInfo pushTokenInfo = new PushTokenInfo(pushTokenOpt.get());
            
            logger.debug("Push token retrieved for user {}", currentUser.getId());
            return ResponseEntity.ok(Map.of(
                    "message", "Push token retrieved successfully",
                    "pushToken", pushTokenInfo
            ));
            
        } catch (Exception e) {
            logger.error("Error retrieving push token", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to retrieve push token"));
        }
    }
    
    /**
     * Update existing push token for the authenticated user.
     * Only available for users with active mobile sessions.
     * 
     * @param pushTokenRequest the push token update request
     * @return success response with updated push token info
     */
    @PutMapping
    public ResponseEntity<?> updatePushToken(@Valid @RequestBody PushTokenRequest pushTokenRequest) {
        logger.debug("Push token update request received");
        
        try {
            User currentUser = getCurrentUser();
            
            // Validate that user has an active mobile session
            if (!authenticationService.hasActiveSession(currentUser, SessionType.MOBILE)) {
                logger.warn("Push token update attempted by user {} without active mobile session", 
                           currentUser.getId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Push tokens can only be managed from mobile sessions"));
            }
            
            // Validate device type for mobile
            pushTokenService.validateMobileDeviceType(pushTokenRequest.getDeviceType());
            
            // Check if user has existing push token
            if (!pushTokenService.hasPushToken(currentUser)) {
                logger.warn("Push token update attempted by user {} without existing token", currentUser.getId());
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "No existing push token to update. Use POST to register a new token."));
            }
            
            // Update push token
            PushToken pushToken = pushTokenService.updatePushToken(
                    currentUser,
                    pushTokenRequest.getPushToken(),
                    pushTokenRequest.getDeviceType(),
                    pushTokenRequest.getDeviceId()
            );
            
            // Create response
            PushTokenInfo pushTokenInfo = new PushTokenInfo(pushToken);
            
            logger.info("Push token updated successfully for user {} with device type {}", 
                       currentUser.getId(), pushTokenRequest.getDeviceType());
            
            return ResponseEntity.ok(Map.of(
                    "message", "Push token updated successfully",
                    "pushToken", pushTokenInfo
            ));
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid push token update request: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error updating push token", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to update push token"));
        }
    }
    
    /**
     * Delete push token for the authenticated user.
     * Only available for users with active mobile sessions.
     * 
     * @return success response
     */
    @DeleteMapping
    public ResponseEntity<?> deletePushToken() {
        logger.debug("SECURITY_EVENT: Push token deletion request - received push token deletion request");
        
        try {
            User currentUser = getCurrentUser();
            
            // Validate that user has an active mobile session
            if (!authenticationService.hasActiveSession(currentUser, SessionType.MOBILE)) {
                logger.warn("SECURITY_EVENT: Push token deletion denied - user: {} attempted deletion without active mobile session", 
                           currentUser.getId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Push tokens can only be managed from mobile sessions"));
            }
            
            // Remove push token
            boolean removed = pushTokenService.removePushToken(currentUser);
            
            if (!removed) {
                logger.debug("SECURITY_EVENT: Push token deletion skipped - no push token found for user: {}", 
                            currentUser.getId());
                return ResponseEntity.ok(Map.of("message", "No push token found to delete"));
            }
            
            logger.info("SECURITY_EVENT: Push token deletion successful - push token deleted for user: {}", 
                       currentUser.getId());
            return ResponseEntity.ok(Map.of("message", "Push token deleted successfully"));
            
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Push token deletion error - unexpected error: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete push token"));
        }
    }
    
    /**
     * Check if the authenticated user has a push token.
     * Only available for users with active mobile sessions.
     * 
     * @return response indicating if user has push token
     */
    @GetMapping("/exists")
    public ResponseEntity<?> hasPushToken() {
        logger.debug("Push token existence check request received");
        
        try {
            User currentUser = getCurrentUser();
            
            // Validate that user has an active mobile session
            if (!authenticationService.hasActiveSession(currentUser, SessionType.MOBILE)) {
                logger.warn("Push token existence check attempted by user {} without active mobile session", 
                           currentUser.getId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(Map.of("error", "Push tokens can only be accessed from mobile sessions"));
            }
            
            boolean hasPushToken = pushTokenService.hasPushToken(currentUser);
            
            logger.debug("Push token existence check completed for user {}: {}", currentUser.getId(), hasPushToken);
            return ResponseEntity.ok(Map.of(
                    "hasPushToken", hasPushToken,
                    "message", hasPushToken ? "User has push token" : "User does not have push token"
            ));
            
        } catch (Exception e) {
            logger.error("Error checking push token existence", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to check push token existence"));
        }
    }
    
    /**
     * Get the current authenticated user from security context.
     * 
     * @return the current user
     * @throws RuntimeException if user is not authenticated
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("User not authenticated");
        }
        
        String email = authentication.getName();
        return userService.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found: " + email));
    }
}