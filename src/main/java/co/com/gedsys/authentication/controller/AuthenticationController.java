package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.ChangePasswordRequest;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.dto.RefreshTokenRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.service.AuthenticationService;
import co.com.gedsys.authentication.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST controller for authentication operations.
 * Handles login, logout, token refresh, profile management, and password changes.
 */
@RestController
@RequestMapping("/auth")
public class AuthenticationController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationController.class);
    
    private final AuthenticationService authenticationService;
    private final UserService userService;
    
    public AuthenticationController(AuthenticationService authenticationService, UserService userService) {
        this.authenticationService = authenticationService;
        this.userService = userService;
    }
    
    /**
     * Login endpoint with session type handling.
     * Supports both mobile and web sessions with optional push token registration.
     * 
     * @param loginRequest the login request containing credentials and session info
     * @return authentication response with JWT and refresh tokens
     */
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        logger.info("SECURITY_EVENT: Login request - received login request for email: {} with session type: {}", 
                   loginRequest.getEmail(), loginRequest.getSessionType());
        
        AuthResponse response = authenticationService.login(loginRequest);
        
        logger.info("SECURITY_EVENT: Login successful - user authenticated for email: {}", loginRequest.getEmail());
        return ResponseEntity.ok(response);
    }
    
    /**
     * Refresh token endpoint with single-use enforcement.
     * Validates and consumes the refresh token, generating new tokens.
     * 
     * @param refreshTokenRequest the refresh token request
     * @return new authentication response with fresh tokens
     */
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest) {
        logger.debug("SECURITY_EVENT: Token refresh request - received token refresh request");
        
        AuthResponse response = authenticationService.refreshToken(refreshTokenRequest.getRefreshToken());
        
        logger.debug("SECURITY_EVENT: Token refresh successful - new tokens generated");
        return ResponseEntity.ok(response);
    }
    
    /**
     * Logout endpoint with proper cleanup.
     * Invalidates refresh tokens and removes push tokens based on session type.
     * 
     * @param request the logout request containing session type
     * @return success response
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, String>> logout(@RequestBody Map<String, String> request) {
        logger.debug("SECURITY_EVENT: Logout request - received logout request");
        
        try {
            User currentUser = getCurrentUser();
            String sessionTypeStr = request.get("sessionType");
            
            if (sessionTypeStr != null) {
                SessionType sessionType = SessionType.valueOf(sessionTypeStr.toUpperCase());
                authenticationService.logout(currentUser, sessionType);
                logger.info("SECURITY_EVENT: Logout successful - user: {} logged out from session type: {}", 
                           currentUser.getId(), sessionType);
            } else {
                // Logout from all sessions if no session type specified
                authenticationService.logoutFromAllSessions(currentUser);
                logger.info("SECURITY_EVENT: Global logout successful - user: {} logged out from all sessions", 
                           currentUser.getId());
            }
            
            return ResponseEntity.ok(Map.of("message", "Logout successful"));
            
        } catch (IllegalArgumentException e) {
            logger.warn("SECURITY_EVENT: Logout failed - invalid session type in request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid session type"));
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Logout error - unexpected error during logout: {}", e.getMessage(), e);
            return ResponseEntity.ok(Map.of("message", "Logout completed"));
        }
    }
    
    /**
     * Profile endpoint for user information.
     * Returns the current authenticated user's profile information.
     * 
     * @return user profile response
     */
    @GetMapping("/profile")
    public ResponseEntity<UserProfileResponse> getProfile() {
        logger.debug("Profile request received");
        
        User currentUser = getCurrentUser();
        UserProfileResponse profile = userService.getUserProfile(currentUser.getId());
        
        logger.debug("Profile retrieved for user: {}", currentUser.getId());
        return ResponseEntity.ok(profile);
    }
    
    /**
     * Change password endpoint with validation.
     * Validates current password and updates to new password.
     * Invalidates all user sessions after successful password change.
     * 
     * @param changePasswordRequest the password change request
     * @return success response
     */
    @PutMapping("/change-password")
    public ResponseEntity<Map<String, String>> changePassword(
            @Valid @RequestBody ChangePasswordRequest changePasswordRequest) {
        logger.debug("SECURITY_EVENT: Password change request - received password change request");
        
        // Validate password confirmation
        if (!changePasswordRequest.isPasswordConfirmationValid()) {
            logger.warn("SECURITY_EVENT: Password change failed - password confirmation mismatch");
            return ResponseEntity.badRequest()
                    .body(Map.of("error", "New password and confirmation do not match"));
        }
        
        // Validate new password is different
        if (!changePasswordRequest.isNewPasswordDifferent()) {
            logger.warn("SECURITY_EVENT: Password change failed - new password same as current password");
            return ResponseEntity.badRequest()
                    .body(Map.of("error", "New password must be different from current password"));
        }
        
        User currentUser = getCurrentUser();
        
        // Change password
        userService.changePassword(
                currentUser.getId(),
                changePasswordRequest.getCurrentPassword(),
                changePasswordRequest.getNewPassword()
        );
        
        // Invalidate all sessions after password change for security
        authenticationService.logoutFromAllSessions(currentUser);
        
        logger.info("SECURITY_EVENT: Password change successful - password changed for user: {} and all sessions invalidated", 
                   currentUser.getId());
        return ResponseEntity.ok(Map.of("message", "Password changed successfully. Please login again."));
    }
    
    /**
     * Validate token endpoint.
     * Validates the current JWT token and returns user information.
     * 
     * @return user profile if token is valid
     */
    @GetMapping("/validate")
    public ResponseEntity<UserProfileResponse> validateToken() {
        logger.debug("Token validation request received");
        
        User currentUser = getCurrentUser();
        UserProfileResponse profile = userService.getUserProfile(currentUser.getId());
        
        logger.debug("Token validation successful for user: {}", currentUser.getId());
        return ResponseEntity.ok(profile);
    }
    
    /**
     * Get current user's active sessions information.
     * 
     * @return session information
     */
    @GetMapping("/sessions")
    public ResponseEntity<Map<String, Object>> getActiveSessions() {
        logger.debug("Active sessions request received");
        
        User currentUser = getCurrentUser();
        
        boolean hasMobileSession = authenticationService.hasActiveSession(currentUser, SessionType.MOBILE);
        boolean hasWebSession = authenticationService.hasActiveSession(currentUser, SessionType.WEB);
        long totalSessions = authenticationService.getActiveSessionCount(currentUser);
        
        Map<String, Object> sessionInfo = Map.of(
                "hasMobileSession", hasMobileSession,
                "hasWebSession", hasWebSession,
                "totalActiveSessions", totalSessions
        );
        
        logger.debug("Active sessions retrieved for user: {}", currentUser.getId());
        return ResponseEntity.ok(sessionInfo);
    }
    

    
    /**
     * Get the current authenticated user from security context.
     * 
     * @return the current user
     * @throws RuntimeException if user is not authenticated
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("User not authenticated");
        }
        
        String email = authentication.getName();
        return userService.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found: " + email));
    }
}